import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Animated,
  TouchableOpacity
} from 'react-native';
import { useAuth } from '../../auth/AuthContext';
import { Message } from '../../types';
import { Button } from '../ui';
import { theme } from '../../styles/theme';

// Props that this component receives from RoomScreen
interface ChatViewProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  roomHost: string;
  // onNextStep is no longer needed here as it's handled globally
}

export const ChatView: React.FC<ChatViewProps> = ({ messages, onSendMessage }) => {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState<string>('');

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      onSendMessage(messageInput.trim());
      setMessageInput('');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      {/* The header no longer needs the menu button */}
      <View style={styles.header}>
        <Text style={styles.title}>自由讨论</Text>
      </View>

      <FlatList
        style={styles.chatList}
        data={messages}
        renderItem={({ item }) => (
          <View style={item.sender === user?.username ? styles.myMessageBubble : styles.theirMessageBubble}>
            <Text style={styles.messageSender}>{item.sender}:</Text>
            <Text style={styles.messageText}>{item.message}</Text>
          </View>
        )}
        keyExtractor={(_, index) => index.toString()}
        inverted
      />
      <View style={styles.inputArea}>
        <TextInput
          style={styles.chatInput}
          placeholder="输入聊天消息..."
          value={messageInput}
          onChangeText={setMessageInput}
        />
        <Button title="发送" onPress={handleSendMessage} />
      </View>

      {/* ActionPanel is removed from here */}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, width: '100%' },
    header: {
        flexDirection: 'row',
        // Center the title as the button is gone
        justifyContent: 'center',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee'
    },
    title: { fontSize: 20, fontWeight: 'bold' },
    // menuButton and menuButtonText styles are no longer needed
    chatList: { flex: 1, paddingHorizontal: 10 },
    inputArea: { flexDirection: 'row', padding: 10, borderTopWidth: 1, borderColor: '#ccc' },
    chatInput: { flex: 1, height: 40, borderWidth: 1, borderColor: 'gray', borderRadius: 20, paddingHorizontal: 15, marginRight: 10 },
    myMessageBubble: { backgroundColor: '#dcf8c6', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-end', marginRight: 10 },
    theirMessageBubble: { backgroundColor: '#f0f0f0', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-start', marginLeft: 10 },
    messageSender: { fontWeight: 'bold', marginBottom: 3 },
    messageText: { fontSize: 16 },
});
