# core/urls.py

from django.urls import path
from .views import (
    HealthCheckView,
    RegisterView,
    RoomCreateView,
    RoomDetailView,
    APIRootView,
    JoinRoomView,
    CustomTokenObtainPairView,
    SubscriptionManagementView,
    ScheduleRoomView,
    CalendarDataView,
    RoomTemplateListView,
    ReservationDetailView,
)
from rest_framework_simplejwt.views import (
    TokenRefreshView,
)

urlpatterns = [
    # --- 添加这一行作为API的根路径 ---
    # 它会匹配到 /api/
    path('', APIRootView.as_view(), name='api-root'),

    # 认证和健康检查
    path('health-check/', HealthCheckView.as_view(), name='health-check'),
    path('register/', RegisterView.as_view(), name='register'),
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # 房间相关
    path('rooms/create/', RoomCreateView.as_view(), name='room-create'),
    path('rooms/schedule/', ScheduleRoomView.as_view(), name='room-schedule'),
    path('rooms/join/', JoinRoomView.as_view(), name='room-join'),
    path('rooms/<str:room_code>/', RoomDetailView.as_view(), name='room-detail'),

    # 订阅管理
    path('subscription/', SubscriptionManagementView.as_view(), name='subscription-management'),

    # 日历和预约相关
    path('calendar/reservations/', CalendarDataView.as_view(), name='calendar-data'),
    path('calendar/reservations/<int:reservation_id>/', ReservationDetailView.as_view(), name='reservation-detail'),
    path('room-templates/', RoomTemplateListView.as_view(), name='room-templates'),
]
